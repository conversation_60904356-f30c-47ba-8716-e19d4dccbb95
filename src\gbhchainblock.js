
const crypto = require('crypto')

class Blockchain{
    constructor(){
        this.Blockchain=[]
        this.data=[]
        this.difficulty=4
        // const hash = this.computeHash(0,'0',new Date().getTime(),'Hello gbhblockchain!',1)
        // console.log(hash)
    }
    mine(){
        let nonce = 0
        const index = 0
        const data = 'Hello gbhblockchain!'
        const prevHash = '0'
        const timestamp = 1693801671206
        let hash = this.computeHash(index, prevHash, timestamp, data, nonce)
        while(hash.slice(0,this.difficulty) !== '0'.repeat(this.difficulty)){
            nonce +=1
            hash = this.computeHash(index, prevHash, timestamp, data, nonce)
            // console.log(nonce,hash)
        }
        console.log('mine over',{
            nonce,
            hash
        })
    }
    
    
    computeHash(index, prevHash, timestamp, data, nonce){
        return crypto
                    .createHash('sha256')
                    .update(index+prevHash+timestamp+data+nonce)
                    .digest('hex')
    }
}


let bc = new Blockchain()
bc.mine()